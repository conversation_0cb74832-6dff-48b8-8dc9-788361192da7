{"version": 3, "sources": ["../../vanta/dist/vanta.waves.min.js"], "sourcesContent": ["!function(t,e){\"object\"==typeof exports&&\"object\"==typeof module?module.exports=e():\"function\"==typeof define&&define.amd?define([],e):\"object\"==typeof exports?exports._vantaEffect=e():t._vantaEffect=e()}(\"undefined\"!=typeof self?self:this,(()=>(()=>{\"use strict\";var t={d:(e,i)=>{for(var s in i)t.o(i,s)&&!t.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:i[s]})},o:(t,e)=>Object.prototype.hasOwnProperty.call(t,e),r:t=>{\"undefined\"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:\"Module\"}),Object.defineProperty(t,\"__esModule\",{value:!0})}},e={};function i(t,e){return null==t&&(t=0),null==e&&(e=1),Math.floor(t+Math.random()*(e-t+1))}t.r(e),t.d(e,{default:()=>c}),Number.prototype.clamp=function(t,e){return Math.min(Math.max(this,t),e)};function s(t){for(;t.children&&t.children.length>0;)s(t.children[0]),t.remove(t.children[0]);t.geometry&&t.geometry.dispose(),t.material&&(Object.keys(t.material).forEach((e=>{t.material[e]&&null!==t.material[e]&&\"function\"==typeof t.material[e].dispose&&t.material[e].dispose()})),t.material.dispose())}const o=\"object\"==typeof window;let n=o&&window.THREE||{};o&&!window.VANTA&&(window.VANTA={});const r=o&&window.VANTA||{};r.register=(t,e)=>r[t]=t=>new e(t),r.version=\"0.5.24\";const h=function(){return Array.prototype.unshift.call(arguments,\"[VANTA]\"),console.error.apply(this,arguments)};r.VantaBase=class{constructor(t={}){if(!o)return!1;r.current=this,this.windowMouseMoveWrapper=this.windowMouseMoveWrapper.bind(this),this.windowTouchWrapper=this.windowTouchWrapper.bind(this),this.windowGyroWrapper=this.windowGyroWrapper.bind(this),this.resize=this.resize.bind(this),this.animationLoop=this.animationLoop.bind(this),this.restart=this.restart.bind(this);const e=\"function\"==typeof this.getDefaultOptions?this.getDefaultOptions():this.defaultOptions;if(this.options=Object.assign({mouseControls:!0,touchControls:!0,gyroControls:!1,minHeight:200,minWidth:200,scale:1,scaleMobile:1},e),(t instanceof HTMLElement||\"string\"==typeof t)&&(t={el:t}),Object.assign(this.options,t),this.options.THREE&&(n=this.options.THREE),this.el=this.options.el,null==this.el)h('Instance needs \"el\" param!');else if(!(this.options.el instanceof HTMLElement)){const t=this.el;if(this.el=(i=t,document.querySelector(i)),!this.el)return void h(\"Cannot find element\",t)}var i,s;this.prepareEl(),this.initThree(),this.setSize();try{this.init()}catch(t){return h(\"Init error\",t),this.renderer&&this.renderer.domElement&&this.el.removeChild(this.renderer.domElement),void(this.options.backgroundColor&&(console.log(\"[VANTA] Falling back to backgroundColor\"),this.el.style.background=(s=this.options.backgroundColor,\"number\"==typeof s?\"#\"+(\"00000\"+s.toString(16)).slice(-6):s)))}this.initMouse(),this.resize(),this.animationLoop();const a=window.addEventListener;a(\"resize\",this.resize),window.requestAnimationFrame(this.resize),this.options.mouseControls&&(a(\"scroll\",this.windowMouseMoveWrapper),a(\"mousemove\",this.windowMouseMoveWrapper)),this.options.touchControls&&(a(\"touchstart\",this.windowTouchWrapper),a(\"touchmove\",this.windowTouchWrapper)),this.options.gyroControls&&a(\"deviceorientation\",this.windowGyroWrapper)}setOptions(t={}){Object.assign(this.options,t),this.triggerMouseMove()}prepareEl(){let t,e;if(\"undefined\"!=typeof Node&&Node.TEXT_NODE)for(t=0;t<this.el.childNodes.length;t++){const e=this.el.childNodes[t];if(e.nodeType===Node.TEXT_NODE){const t=document.createElement(\"span\");t.textContent=e.textContent,e.parentElement.insertBefore(t,e),e.remove()}}for(t=0;t<this.el.children.length;t++)e=this.el.children[t],\"static\"===getComputedStyle(e).position&&(e.style.position=\"relative\"),\"auto\"===getComputedStyle(e).zIndex&&(e.style.zIndex=1);\"static\"===getComputedStyle(this.el).position&&(this.el.style.position=\"relative\")}applyCanvasStyles(t,e={}){Object.assign(t.style,{position:\"absolute\",zIndex:0,top:0,left:0,background:\"\"}),Object.assign(t.style,e),t.classList.add(\"vanta-canvas\")}initThree(){n.WebGLRenderer?(this.renderer=new n.WebGLRenderer({alpha:!0,antialias:!0}),this.el.appendChild(this.renderer.domElement),this.applyCanvasStyles(this.renderer.domElement),isNaN(this.options.backgroundAlpha)&&(this.options.backgroundAlpha=1),this.scene=new n.Scene):console.warn(\"[VANTA] No THREE defined on window\")}getCanvasElement(){return this.renderer?this.renderer.domElement:this.p5renderer?this.p5renderer.canvas:void 0}getCanvasRect(){const t=this.getCanvasElement();return!!t&&t.getBoundingClientRect()}windowMouseMoveWrapper(t){const e=this.getCanvasRect();if(!e)return!1;const i=t.clientX-e.left,s=t.clientY-e.top;i>=0&&s>=0&&i<=e.width&&s<=e.height&&(this.mouseX=i,this.mouseY=s,this.options.mouseEase||this.triggerMouseMove(i,s))}windowTouchWrapper(t){const e=this.getCanvasRect();if(!e)return!1;if(1===t.touches.length){const i=t.touches[0].clientX-e.left,s=t.touches[0].clientY-e.top;i>=0&&s>=0&&i<=e.width&&s<=e.height&&(this.mouseX=i,this.mouseY=s,this.options.mouseEase||this.triggerMouseMove(i,s))}}windowGyroWrapper(t){const e=this.getCanvasRect();if(!e)return!1;const i=Math.round(2*t.alpha)-e.left,s=Math.round(2*t.beta)-e.top;i>=0&&s>=0&&i<=e.width&&s<=e.height&&(this.mouseX=i,this.mouseY=s,this.options.mouseEase||this.triggerMouseMove(i,s))}triggerMouseMove(t,e){void 0===t&&void 0===e&&(this.options.mouseEase?(t=this.mouseEaseX,e=this.mouseEaseY):(t=this.mouseX,e=this.mouseY)),this.uniforms&&(this.uniforms.iMouse.value.x=t/this.scale,this.uniforms.iMouse.value.y=e/this.scale);const i=t/this.width,s=e/this.height;\"function\"==typeof this.onMouseMove&&this.onMouseMove(i,s)}setSize(){this.scale||(this.scale=1),\"undefined\"!=typeof navigator&&(/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)||window.innerWidth<600)&&this.options.scaleMobile?this.scale=this.options.scaleMobile:this.options.scale&&(this.scale=this.options.scale),this.width=Math.max(this.el.offsetWidth,this.options.minWidth),this.height=Math.max(this.el.offsetHeight,this.options.minHeight)}initMouse(){(!this.mouseX&&!this.mouseY||this.mouseX===this.options.minWidth/2&&this.mouseY===this.options.minHeight/2)&&(this.mouseX=this.width/2,this.mouseY=this.height/2,this.triggerMouseMove(this.mouseX,this.mouseY))}resize(){this.setSize(),this.camera&&(this.camera.aspect=this.width/this.height,\"function\"==typeof this.camera.updateProjectionMatrix&&this.camera.updateProjectionMatrix()),this.renderer&&(this.renderer.setSize(this.width,this.height),this.renderer.setPixelRatio(window.devicePixelRatio/this.scale)),\"function\"==typeof this.onResize&&this.onResize()}isOnScreen(){const t=this.el.offsetHeight,e=this.el.getBoundingClientRect(),i=window.pageYOffset||(document.documentElement||document.body.parentNode||document.body).scrollTop,s=e.top+i;return s-window.innerHeight<=i&&i<=s+t}animationLoop(){this.t||(this.t=0),this.t2||(this.t2=0);const t=performance.now();if(this.prevNow){let e=(t-this.prevNow)/(1e3/60);e=Math.max(.2,Math.min(e,5)),this.t+=e,this.t2+=(this.options.speed||1)*e,this.uniforms&&(this.uniforms.iTime.value=.016667*this.t2)}return this.prevNow=t,this.options.mouseEase&&(this.mouseEaseX=this.mouseEaseX||this.mouseX||0,this.mouseEaseY=this.mouseEaseY||this.mouseY||0,Math.abs(this.mouseEaseX-this.mouseX)+Math.abs(this.mouseEaseY-this.mouseY)>.1&&(this.mouseEaseX+=.05*(this.mouseX-this.mouseEaseX),this.mouseEaseY+=.05*(this.mouseY-this.mouseEaseY),this.triggerMouseMove(this.mouseEaseX,this.mouseEaseY))),(this.isOnScreen()||this.options.forceAnimate)&&(\"function\"==typeof this.onUpdate&&this.onUpdate(),this.scene&&this.camera&&(this.renderer.render(this.scene,this.camera),this.renderer.setClearColor(this.options.backgroundColor,this.options.backgroundAlpha)),this.fps&&this.fps.update&&this.fps.update(),\"function\"==typeof this.afterRender&&this.afterRender()),this.req=window.requestAnimationFrame(this.animationLoop)}restart(){if(this.scene)for(;this.scene.children.length;)this.scene.remove(this.scene.children[0]);\"function\"==typeof this.onRestart&&this.onRestart(),this.init()}init(){\"function\"==typeof this.onInit&&this.onInit()}destroy(){\"function\"==typeof this.onDestroy&&this.onDestroy();const t=window.removeEventListener;t(\"touchstart\",this.windowTouchWrapper),t(\"touchmove\",this.windowTouchWrapper),t(\"scroll\",this.windowMouseMoveWrapper),t(\"mousemove\",this.windowMouseMoveWrapper),t(\"deviceorientation\",this.windowGyroWrapper),t(\"resize\",this.resize),window.cancelAnimationFrame(this.req);const e=this.scene;e&&e.children&&s(e),this.renderer&&(this.renderer.domElement&&this.el.removeChild(this.renderer.domElement),this.renderer=null,this.scene=null),r.current===this&&(r.current=null)}};const a=r.VantaBase;let p=\"object\"==typeof window&&window.THREE;class l extends a{static initClass(){this.prototype.ww=100,this.prototype.hh=80,this.prototype.waveNoise=4}constructor(t){p=t.THREE||p,super(t)}getMaterial(){const t={color:this.options.color,shininess:this.options.shininess,flatShading:!0,side:p.DoubleSide};return new p.MeshPhongMaterial(t)}onInit(){let t,e;const s=this.getMaterial(),o=new p.BufferGeometry;this.gg=[];const n=[];for(t=0;t<=this.ww;t++)for(this.gg[t]=[],e=0;e<=this.hh;e++){const i=n.length,s=new p.Vector3(18*(t-.5*this.ww),(null==(r=0)&&(r=0),null==(h=this.waveNoise)&&(h=1),r+Math.random()*(h-r)-10),18*(.5*this.hh-e));n.push(s),this.gg[t][e]=i}var r,h;o.setFromPoints(n);const a=[];for(t=1;t<=this.ww;t++)for(e=1;e<=this.hh;e++){let s,o;const n=this.gg[t][e],r=this.gg[t][e-1],h=this.gg[t-1][e],p=this.gg[t-1][e-1];i(0,1)?(s=[p,r,h],o=[r,h,n]):(s=[p,r,n],o=[p,h,n]),a.push(...s,...o)}o.setIndex(a),this.plane=new p.Mesh(o,s),this.scene.add(this.plane);const l=new p.AmbientLight(16777215,.9);this.scene.add(l);const c=new p.PointLight(16777215,.9);c.position.set(-100,250,-100),this.scene.add(c),this.camera=new p.PerspectiveCamera(35,this.width/this.height,50,1e4),this.cameraPosition=new p.Vector3(240,200,390),this.cameraTarget=new p.Vector3(140,-30,190),this.camera.position.copy(this.cameraPosition),this.scene.add(this.camera)}onUpdate(){let t;this.plane.material.color.set(this.options.color),this.plane.material.shininess=this.options.shininess,this.camera.ox=this.cameraPosition.x/this.options.zoom,this.camera.oy=this.cameraPosition.y/this.options.zoom,this.camera.oz=this.cameraPosition.z/this.options.zoom,null!=this.controls&&this.controls.update();const e=this.camera;Math.abs(e.tx-e.position.x)>.01&&(t=e.tx-e.position.x,e.position.x+=.02*t),Math.abs(e.ty-e.position.y)>.01&&(t=e.ty-e.position.y,e.position.y+=.02*t),Math.abs(e.tz-e.position.z)>.01&&(t=e.tz-e.position.z,e.position.z+=.02*t),e.lookAt(this.cameraTarget),this.oy=this.oy||{};for(let t=0;t<this.plane.geometry.attributes.position.array.length;t+=3){const e={x:this.plane.geometry.attributes.position.array[t],y:this.plane.geometry.attributes.position.array[t+1],z:this.plane.geometry.attributes.position.array[t+2],oy:this.oy[t]};if(e.oy){const i=this.options.waveSpeed,s=Math.sqrt(i)*Math.cos(-e.x-.7*e.z),o=Math.sin(i*this.t*.02-i*e.x*.025+i*e.z*.015+s),n=Math.pow(o+1,2)/4;e.y=e.oy+n*this.options.waveHeight,this.plane.geometry.attributes.position.array[t+1]=e.y}else this.oy[t]=e.y}this.plane.geometry.attributes.position.setUsage(p.DynamicDrawUsage),this.plane.geometry.computeVertexNormals(),this.plane.geometry.attributes.position.needsUpdate=!0,this.wireframe&&(this.wireframe.geometry.fromGeometry(this.plane.geometry),this.wireframe.geometry.computeFaceNormals())}onMouseMove(t,e){const i=this.camera;return i.oy||(i.oy=i.position.y,i.ox=i.position.x,i.oz=i.position.z),i.tx=i.ox+100*(t-.5)/this.options.zoom,i.ty=i.oy+-100*(e-.5)/this.options.zoom,i.tz=i.oz+-50*(t-.5)/this.options.zoom}}l.prototype.defaultOptions={color:21896,shininess:30,waveHeight:15,waveSpeed:1,zoom:1},l.initClass();const c=r.register(\"WAVES\",l);return e})()));"], "mappings": ";;;;;AAAA;AAAA;AAAA,KAAC,SAAS,GAAE,GAAE;AAAC,kBAAU,OAAO,WAAS,YAAU,OAAO,SAAO,OAAO,UAAQ,EAAE,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,GAAE,CAAC,IAAE,YAAU,OAAO,UAAQ,QAAQ,eAAa,EAAE,IAAE,EAAE,eAAa,EAAE;AAAA,IAAC,EAAE,eAAa,OAAO,OAAK,OAAK,SAAM,OAAK,MAAI;AAAC;AAAa,UAAI,IAAE,EAAC,GAAE,CAACA,IAAEC,OAAI;AAAC,iBAAQC,MAAKD,GAAE,GAAE,EAAEA,IAAEC,EAAC,KAAG,CAAC,EAAE,EAAEF,IAAEE,EAAC,KAAG,OAAO,eAAeF,IAAEE,IAAE,EAAC,YAAW,MAAG,KAAID,GAAEC,EAAC,EAAC,CAAC;AAAA,MAAC,GAAE,GAAE,CAACC,IAAEH,OAAI,OAAO,UAAU,eAAe,KAAKG,IAAEH,EAAC,GAAE,GAAE,CAAAG,OAAG;AAAC,uBAAa,OAAO,UAAQ,OAAO,eAAa,OAAO,eAAeA,IAAE,OAAO,aAAY,EAAC,OAAM,SAAQ,CAAC,GAAE,OAAO,eAAeA,IAAE,cAAa,EAAC,OAAM,KAAE,CAAC;AAAA,MAAC,EAAC,GAAE,IAAE,CAAC;AAAE,eAAS,EAAEA,IAAEH,IAAE;AAAC,eAAO,QAAMG,OAAIA,KAAE,IAAG,QAAMH,OAAIA,KAAE,IAAG,KAAK,MAAMG,KAAE,KAAK,OAAO,KAAGH,KAAEG,KAAE,EAAE;AAAA,MAAC;AAAC,QAAE,EAAE,CAAC,GAAE,EAAE,EAAE,GAAE,EAAC,SAAQ,MAAI,EAAC,CAAC,GAAE,OAAO,UAAU,QAAM,SAASA,IAAEH,IAAE;AAAC,eAAO,KAAK,IAAI,KAAK,IAAI,MAAKG,EAAC,GAAEH,EAAC;AAAA,MAAC;AAAE,eAAS,EAAEG,IAAE;AAAC,eAAKA,GAAE,YAAUA,GAAE,SAAS,SAAO,IAAG,GAAEA,GAAE,SAAS,CAAC,CAAC,GAAEA,GAAE,OAAOA,GAAE,SAAS,CAAC,CAAC;AAAE,QAAAA,GAAE,YAAUA,GAAE,SAAS,QAAQ,GAAEA,GAAE,aAAW,OAAO,KAAKA,GAAE,QAAQ,EAAE,QAAS,CAAAH,OAAG;AAAC,UAAAG,GAAE,SAASH,EAAC,KAAG,SAAOG,GAAE,SAASH,EAAC,KAAG,cAAY,OAAOG,GAAE,SAASH,EAAC,EAAE,WAASG,GAAE,SAASH,EAAC,EAAE,QAAQ;AAAA,QAAC,CAAE,GAAEG,GAAE,SAAS,QAAQ;AAAA,MAAE;AAAC,YAAM,IAAE,YAAU,OAAO;AAAO,UAAI,IAAE,KAAG,OAAO,SAAO,CAAC;AAAE,WAAG,CAAC,OAAO,UAAQ,OAAO,QAAM,CAAC;AAAG,YAAM,IAAE,KAAG,OAAO,SAAO,CAAC;AAAE,QAAE,WAAS,CAACA,IAAEH,OAAI,EAAEG,EAAC,IAAE,CAAAA,OAAG,IAAIH,GAAEG,EAAC,GAAE,EAAE,UAAQ;AAAS,YAAM,IAAE,WAAU;AAAC,eAAO,MAAM,UAAU,QAAQ,KAAK,WAAU,SAAS,GAAE,QAAQ,MAAM,MAAM,MAAK,SAAS;AAAA,MAAC;AAAE,QAAE,YAAU,MAAK;AAAA,QAAC,YAAYA,KAAE,CAAC,GAAE;AAAC,cAAG,CAAC,EAAE,QAAM;AAAG,YAAE,UAAQ,MAAK,KAAK,yBAAuB,KAAK,uBAAuB,KAAK,IAAI,GAAE,KAAK,qBAAmB,KAAK,mBAAmB,KAAK,IAAI,GAAE,KAAK,oBAAkB,KAAK,kBAAkB,KAAK,IAAI,GAAE,KAAK,SAAO,KAAK,OAAO,KAAK,IAAI,GAAE,KAAK,gBAAc,KAAK,cAAc,KAAK,IAAI,GAAE,KAAK,UAAQ,KAAK,QAAQ,KAAK,IAAI;AAAE,gBAAMH,KAAE,cAAY,OAAO,KAAK,oBAAkB,KAAK,kBAAkB,IAAE,KAAK;AAAe,cAAG,KAAK,UAAQ,OAAO,OAAO,EAAC,eAAc,MAAG,eAAc,MAAG,cAAa,OAAG,WAAU,KAAI,UAAS,KAAI,OAAM,GAAE,aAAY,EAAC,GAAEA,EAAC,IAAGG,cAAa,eAAa,YAAU,OAAOA,QAAKA,KAAE,EAAC,IAAGA,GAAC,IAAG,OAAO,OAAO,KAAK,SAAQA,EAAC,GAAE,KAAK,QAAQ,UAAQ,IAAE,KAAK,QAAQ,QAAO,KAAK,KAAG,KAAK,QAAQ,IAAG,QAAM,KAAK,GAAG,GAAE,4BAA4B;AAAA,mBAAU,EAAE,KAAK,QAAQ,cAAc,cAAa;AAAC,kBAAMA,KAAE,KAAK;AAAG,gBAAG,KAAK,MAAIF,KAAEE,IAAE,SAAS,cAAcF,EAAC,IAAG,CAAC,KAAK,GAAG,QAAO,KAAK,EAAE,uBAAsBE,EAAC;AAAA,UAAC;AAAC,cAAIF,IAAEC;AAAE,eAAK,UAAU,GAAE,KAAK,UAAU,GAAE,KAAK,QAAQ;AAAE,cAAG;AAAC,iBAAK,KAAK;AAAA,UAAC,SAAOC,IAAE;AAAC,mBAAO,EAAE,cAAaA,EAAC,GAAE,KAAK,YAAU,KAAK,SAAS,cAAY,KAAK,GAAG,YAAY,KAAK,SAAS,UAAU,GAAE,MAAK,KAAK,QAAQ,oBAAkB,QAAQ,IAAI,yCAAyC,GAAE,KAAK,GAAG,MAAM,cAAYD,KAAE,KAAK,QAAQ,iBAAgB,YAAU,OAAOA,KAAE,OAAK,UAAQA,GAAE,SAAS,EAAE,GAAG,MAAM,EAAE,IAAEA;AAAA,UAAI;AAAC,eAAK,UAAU,GAAE,KAAK,OAAO,GAAE,KAAK,cAAc;AAAE,gBAAME,KAAE,OAAO;AAAiB,UAAAA,GAAE,UAAS,KAAK,MAAM,GAAE,OAAO,sBAAsB,KAAK,MAAM,GAAE,KAAK,QAAQ,kBAAgBA,GAAE,UAAS,KAAK,sBAAsB,GAAEA,GAAE,aAAY,KAAK,sBAAsB,IAAG,KAAK,QAAQ,kBAAgBA,GAAE,cAAa,KAAK,kBAAkB,GAAEA,GAAE,aAAY,KAAK,kBAAkB,IAAG,KAAK,QAAQ,gBAAcA,GAAE,qBAAoB,KAAK,iBAAiB;AAAA,QAAC;AAAA,QAAC,WAAWD,KAAE,CAAC,GAAE;AAAC,iBAAO,OAAO,KAAK,SAAQA,EAAC,GAAE,KAAK,iBAAiB;AAAA,QAAC;AAAA,QAAC,YAAW;AAAC,cAAIA,IAAEH;AAAE,cAAG,eAAa,OAAO,QAAM,KAAK,UAAU,MAAIG,KAAE,GAAEA,KAAE,KAAK,GAAG,WAAW,QAAOA,MAAI;AAAC,kBAAMH,KAAE,KAAK,GAAG,WAAWG,EAAC;AAAE,gBAAGH,GAAE,aAAW,KAAK,WAAU;AAAC,oBAAMG,KAAE,SAAS,cAAc,MAAM;AAAE,cAAAA,GAAE,cAAYH,GAAE,aAAYA,GAAE,cAAc,aAAaG,IAAEH,EAAC,GAAEA,GAAE,OAAO;AAAA,YAAC;AAAA,UAAC;AAAC,eAAIG,KAAE,GAAEA,KAAE,KAAK,GAAG,SAAS,QAAOA,KAAI,CAAAH,KAAE,KAAK,GAAG,SAASG,EAAC,GAAE,aAAW,iBAAiBH,EAAC,EAAE,aAAWA,GAAE,MAAM,WAAS,aAAY,WAAS,iBAAiBA,EAAC,EAAE,WAASA,GAAE,MAAM,SAAO;AAAG,uBAAW,iBAAiB,KAAK,EAAE,EAAE,aAAW,KAAK,GAAG,MAAM,WAAS;AAAA,QAAW;AAAA,QAAC,kBAAkBG,IAAEH,KAAE,CAAC,GAAE;AAAC,iBAAO,OAAOG,GAAE,OAAM,EAAC,UAAS,YAAW,QAAO,GAAE,KAAI,GAAE,MAAK,GAAE,YAAW,GAAE,CAAC,GAAE,OAAO,OAAOA,GAAE,OAAMH,EAAC,GAAEG,GAAE,UAAU,IAAI,cAAc;AAAA,QAAC;AAAA,QAAC,YAAW;AAAC,YAAE,iBAAe,KAAK,WAAS,IAAI,EAAE,cAAc,EAAC,OAAM,MAAG,WAAU,KAAE,CAAC,GAAE,KAAK,GAAG,YAAY,KAAK,SAAS,UAAU,GAAE,KAAK,kBAAkB,KAAK,SAAS,UAAU,GAAE,MAAM,KAAK,QAAQ,eAAe,MAAI,KAAK,QAAQ,kBAAgB,IAAG,KAAK,QAAM,IAAI,EAAE,WAAO,QAAQ,KAAK,oCAAoC;AAAA,QAAC;AAAA,QAAC,mBAAkB;AAAC,iBAAO,KAAK,WAAS,KAAK,SAAS,aAAW,KAAK,aAAW,KAAK,WAAW,SAAO;AAAA,QAAM;AAAA,QAAC,gBAAe;AAAC,gBAAMA,KAAE,KAAK,iBAAiB;AAAE,iBAAM,CAAC,CAACA,MAAGA,GAAE,sBAAsB;AAAA,QAAC;AAAA,QAAC,uBAAuBA,IAAE;AAAC,gBAAMH,KAAE,KAAK,cAAc;AAAE,cAAG,CAACA,GAAE,QAAM;AAAG,gBAAMC,KAAEE,GAAE,UAAQH,GAAE,MAAKE,KAAEC,GAAE,UAAQH,GAAE;AAAI,UAAAC,MAAG,KAAGC,MAAG,KAAGD,MAAGD,GAAE,SAAOE,MAAGF,GAAE,WAAS,KAAK,SAAOC,IAAE,KAAK,SAAOC,IAAE,KAAK,QAAQ,aAAW,KAAK,iBAAiBD,IAAEC,EAAC;AAAA,QAAE;AAAA,QAAC,mBAAmBC,IAAE;AAAC,gBAAMH,KAAE,KAAK,cAAc;AAAE,cAAG,CAACA,GAAE,QAAM;AAAG,cAAG,MAAIG,GAAE,QAAQ,QAAO;AAAC,kBAAMF,KAAEE,GAAE,QAAQ,CAAC,EAAE,UAAQH,GAAE,MAAKE,KAAEC,GAAE,QAAQ,CAAC,EAAE,UAAQH,GAAE;AAAI,YAAAC,MAAG,KAAGC,MAAG,KAAGD,MAAGD,GAAE,SAAOE,MAAGF,GAAE,WAAS,KAAK,SAAOC,IAAE,KAAK,SAAOC,IAAE,KAAK,QAAQ,aAAW,KAAK,iBAAiBD,IAAEC,EAAC;AAAA,UAAE;AAAA,QAAC;AAAA,QAAC,kBAAkBC,IAAE;AAAC,gBAAMH,KAAE,KAAK,cAAc;AAAE,cAAG,CAACA,GAAE,QAAM;AAAG,gBAAMC,KAAE,KAAK,MAAM,IAAEE,GAAE,KAAK,IAAEH,GAAE,MAAKE,KAAE,KAAK,MAAM,IAAEC,GAAE,IAAI,IAAEH,GAAE;AAAI,UAAAC,MAAG,KAAGC,MAAG,KAAGD,MAAGD,GAAE,SAAOE,MAAGF,GAAE,WAAS,KAAK,SAAOC,IAAE,KAAK,SAAOC,IAAE,KAAK,QAAQ,aAAW,KAAK,iBAAiBD,IAAEC,EAAC;AAAA,QAAE;AAAA,QAAC,iBAAiBC,IAAEH,IAAE;AAAC,qBAASG,MAAG,WAASH,OAAI,KAAK,QAAQ,aAAWG,KAAE,KAAK,YAAWH,KAAE,KAAK,eAAaG,KAAE,KAAK,QAAOH,KAAE,KAAK,UAAS,KAAK,aAAW,KAAK,SAAS,OAAO,MAAM,IAAEG,KAAE,KAAK,OAAM,KAAK,SAAS,OAAO,MAAM,IAAEH,KAAE,KAAK;AAAO,gBAAMC,KAAEE,KAAE,KAAK,OAAMD,KAAEF,KAAE,KAAK;AAAO,wBAAY,OAAO,KAAK,eAAa,KAAK,YAAYC,IAAEC,EAAC;AAAA,QAAC;AAAA,QAAC,UAAS;AAAC,eAAK,UAAQ,KAAK,QAAM,IAAG,eAAa,OAAO,cAAY,iEAAiE,KAAK,UAAU,SAAS,KAAG,OAAO,aAAW,QAAM,KAAK,QAAQ,cAAY,KAAK,QAAM,KAAK,QAAQ,cAAY,KAAK,QAAQ,UAAQ,KAAK,QAAM,KAAK,QAAQ,QAAO,KAAK,QAAM,KAAK,IAAI,KAAK,GAAG,aAAY,KAAK,QAAQ,QAAQ,GAAE,KAAK,SAAO,KAAK,IAAI,KAAK,GAAG,cAAa,KAAK,QAAQ,SAAS;AAAA,QAAC;AAAA,QAAC,YAAW;AAAC,WAAC,CAAC,KAAK,UAAQ,CAAC,KAAK,UAAQ,KAAK,WAAS,KAAK,QAAQ,WAAS,KAAG,KAAK,WAAS,KAAK,QAAQ,YAAU,OAAK,KAAK,SAAO,KAAK,QAAM,GAAE,KAAK,SAAO,KAAK,SAAO,GAAE,KAAK,iBAAiB,KAAK,QAAO,KAAK,MAAM;AAAA,QAAE;AAAA,QAAC,SAAQ;AAAC,eAAK,QAAQ,GAAE,KAAK,WAAS,KAAK,OAAO,SAAO,KAAK,QAAM,KAAK,QAAO,cAAY,OAAO,KAAK,OAAO,0BAAwB,KAAK,OAAO,uBAAuB,IAAG,KAAK,aAAW,KAAK,SAAS,QAAQ,KAAK,OAAM,KAAK,MAAM,GAAE,KAAK,SAAS,cAAc,OAAO,mBAAiB,KAAK,KAAK,IAAG,cAAY,OAAO,KAAK,YAAU,KAAK,SAAS;AAAA,QAAC;AAAA,QAAC,aAAY;AAAC,gBAAMC,KAAE,KAAK,GAAG,cAAaH,KAAE,KAAK,GAAG,sBAAsB,GAAEC,KAAE,OAAO,gBAAc,SAAS,mBAAiB,SAAS,KAAK,cAAY,SAAS,MAAM,WAAUC,KAAEF,GAAE,MAAIC;AAAE,iBAAOC,KAAE,OAAO,eAAaD,MAAGA,MAAGC,KAAEC;AAAA,QAAC;AAAA,QAAC,gBAAe;AAAC,eAAK,MAAI,KAAK,IAAE,IAAG,KAAK,OAAK,KAAK,KAAG;AAAG,gBAAMA,KAAE,YAAY,IAAI;AAAE,cAAG,KAAK,SAAQ;AAAC,gBAAIH,MAAGG,KAAE,KAAK,YAAU,MAAI;AAAI,YAAAH,KAAE,KAAK,IAAI,KAAG,KAAK,IAAIA,IAAE,CAAC,CAAC,GAAE,KAAK,KAAGA,IAAE,KAAK,OAAK,KAAK,QAAQ,SAAO,KAAGA,IAAE,KAAK,aAAW,KAAK,SAAS,MAAM,QAAM,WAAQ,KAAK;AAAA,UAAG;AAAC,iBAAO,KAAK,UAAQG,IAAE,KAAK,QAAQ,cAAY,KAAK,aAAW,KAAK,cAAY,KAAK,UAAQ,GAAE,KAAK,aAAW,KAAK,cAAY,KAAK,UAAQ,GAAE,KAAK,IAAI,KAAK,aAAW,KAAK,MAAM,IAAE,KAAK,IAAI,KAAK,aAAW,KAAK,MAAM,IAAE,QAAK,KAAK,cAAY,QAAK,KAAK,SAAO,KAAK,aAAY,KAAK,cAAY,QAAK,KAAK,SAAO,KAAK,aAAY,KAAK,iBAAiB,KAAK,YAAW,KAAK,UAAU,MAAK,KAAK,WAAW,KAAG,KAAK,QAAQ,kBAAgB,cAAY,OAAO,KAAK,YAAU,KAAK,SAAS,GAAE,KAAK,SAAO,KAAK,WAAS,KAAK,SAAS,OAAO,KAAK,OAAM,KAAK,MAAM,GAAE,KAAK,SAAS,cAAc,KAAK,QAAQ,iBAAgB,KAAK,QAAQ,eAAe,IAAG,KAAK,OAAK,KAAK,IAAI,UAAQ,KAAK,IAAI,OAAO,GAAE,cAAY,OAAO,KAAK,eAAa,KAAK,YAAY,IAAG,KAAK,MAAI,OAAO,sBAAsB,KAAK,aAAa;AAAA,QAAC;AAAA,QAAC,UAAS;AAAC,cAAG,KAAK,MAAM,QAAK,KAAK,MAAM,SAAS,SAAQ,MAAK,MAAM,OAAO,KAAK,MAAM,SAAS,CAAC,CAAC;AAAE,wBAAY,OAAO,KAAK,aAAW,KAAK,UAAU,GAAE,KAAK,KAAK;AAAA,QAAC;AAAA,QAAC,OAAM;AAAC,wBAAY,OAAO,KAAK,UAAQ,KAAK,OAAO;AAAA,QAAC;AAAA,QAAC,UAAS;AAAC,wBAAY,OAAO,KAAK,aAAW,KAAK,UAAU;AAAE,gBAAMA,KAAE,OAAO;AAAoB,UAAAA,GAAE,cAAa,KAAK,kBAAkB,GAAEA,GAAE,aAAY,KAAK,kBAAkB,GAAEA,GAAE,UAAS,KAAK,sBAAsB,GAAEA,GAAE,aAAY,KAAK,sBAAsB,GAAEA,GAAE,qBAAoB,KAAK,iBAAiB,GAAEA,GAAE,UAAS,KAAK,MAAM,GAAE,OAAO,qBAAqB,KAAK,GAAG;AAAE,gBAAMH,KAAE,KAAK;AAAM,UAAAA,MAAGA,GAAE,YAAU,EAAEA,EAAC,GAAE,KAAK,aAAW,KAAK,SAAS,cAAY,KAAK,GAAG,YAAY,KAAK,SAAS,UAAU,GAAE,KAAK,WAAS,MAAK,KAAK,QAAM,OAAM,EAAE,YAAU,SAAO,EAAE,UAAQ;AAAA,QAAK;AAAA,MAAC;AAAE,YAAM,IAAE,EAAE;AAAU,UAAI,IAAE,YAAU,OAAO,UAAQ,OAAO;AAAA,MAAM,MAAM,UAAU,EAAC;AAAA,QAAC,OAAO,YAAW;AAAC,eAAK,UAAU,KAAG,KAAI,KAAK,UAAU,KAAG,IAAG,KAAK,UAAU,YAAU;AAAA,QAAC;AAAA,QAAC,YAAYG,IAAE;AAAC,cAAEA,GAAE,SAAO,GAAE,MAAMA,EAAC;AAAA,QAAC;AAAA,QAAC,cAAa;AAAC,gBAAMA,KAAE,EAAC,OAAM,KAAK,QAAQ,OAAM,WAAU,KAAK,QAAQ,WAAU,aAAY,MAAG,MAAK,EAAE,WAAU;AAAE,iBAAO,IAAI,EAAE,kBAAkBA,EAAC;AAAA,QAAC;AAAA,QAAC,SAAQ;AAAC,cAAIA,IAAEH;AAAE,gBAAME,KAAE,KAAK,YAAY,GAAEG,KAAE,IAAI,EAAE;AAAe,eAAK,KAAG,CAAC;AAAE,gBAAMC,KAAE,CAAC;AAAE,eAAIH,KAAE,GAAEA,MAAG,KAAK,IAAGA,KAAI,MAAI,KAAK,GAAGA,EAAC,IAAE,CAAC,GAAEH,KAAE,GAAEA,MAAG,KAAK,IAAGA,MAAI;AAAC,kBAAMC,KAAEK,GAAE,QAAOJ,KAAE,IAAI,EAAE,QAAQ,MAAIC,KAAE,MAAG,KAAK,MAAK,SAAOI,KAAE,OAAKA,KAAE,IAAG,SAAOC,KAAE,KAAK,eAAaA,KAAE,IAAGD,KAAE,KAAK,OAAO,KAAGC,KAAED,MAAG,KAAI,MAAI,MAAG,KAAK,KAAGP,GAAE;AAAE,YAAAM,GAAE,KAAKJ,EAAC,GAAE,KAAK,GAAGC,EAAC,EAAEH,EAAC,IAAEC;AAAA,UAAC;AAAC,cAAIM,IAAEC;AAAE,UAAAH,GAAE,cAAcC,EAAC;AAAE,gBAAMF,KAAE,CAAC;AAAE,eAAID,KAAE,GAAEA,MAAG,KAAK,IAAGA,KAAI,MAAIH,KAAE,GAAEA,MAAG,KAAK,IAAGA,MAAI;AAAC,gBAAIE,IAAEG;AAAE,kBAAMC,KAAE,KAAK,GAAGH,EAAC,EAAEH,EAAC,GAAEO,KAAE,KAAK,GAAGJ,EAAC,EAAEH,KAAE,CAAC,GAAEQ,KAAE,KAAK,GAAGL,KAAE,CAAC,EAAEH,EAAC,GAAES,KAAE,KAAK,GAAGN,KAAE,CAAC,EAAEH,KAAE,CAAC;AAAE,cAAE,GAAE,CAAC,KAAGE,KAAE,CAACO,IAAEF,IAAEC,EAAC,GAAEH,KAAE,CAACE,IAAEC,IAAEF,EAAC,MAAIJ,KAAE,CAACO,IAAEF,IAAED,EAAC,GAAED,KAAE,CAACI,IAAED,IAAEF,EAAC,IAAGF,GAAE,KAAK,GAAGF,IAAE,GAAGG,EAAC;AAAA,UAAC;AAAC,UAAAA,GAAE,SAASD,EAAC,GAAE,KAAK,QAAM,IAAI,EAAE,KAAKC,IAAEH,EAAC,GAAE,KAAK,MAAM,IAAI,KAAK,KAAK;AAAE,gBAAMQ,KAAE,IAAI,EAAE,aAAa,UAAS,GAAE;AAAE,eAAK,MAAM,IAAIA,EAAC;AAAE,gBAAMC,KAAE,IAAI,EAAE,WAAW,UAAS,GAAE;AAAE,UAAAA,GAAE,SAAS,IAAI,MAAK,KAAI,IAAI,GAAE,KAAK,MAAM,IAAIA,EAAC,GAAE,KAAK,SAAO,IAAI,EAAE,kBAAkB,IAAG,KAAK,QAAM,KAAK,QAAO,IAAG,GAAG,GAAE,KAAK,iBAAe,IAAI,EAAE,QAAQ,KAAI,KAAI,GAAG,GAAE,KAAK,eAAa,IAAI,EAAE,QAAQ,KAAI,KAAI,GAAG,GAAE,KAAK,OAAO,SAAS,KAAK,KAAK,cAAc,GAAE,KAAK,MAAM,IAAI,KAAK,MAAM;AAAA,QAAC;AAAA,QAAC,WAAU;AAAC,cAAIR;AAAE,eAAK,MAAM,SAAS,MAAM,IAAI,KAAK,QAAQ,KAAK,GAAE,KAAK,MAAM,SAAS,YAAU,KAAK,QAAQ,WAAU,KAAK,OAAO,KAAG,KAAK,eAAe,IAAE,KAAK,QAAQ,MAAK,KAAK,OAAO,KAAG,KAAK,eAAe,IAAE,KAAK,QAAQ,MAAK,KAAK,OAAO,KAAG,KAAK,eAAe,IAAE,KAAK,QAAQ,MAAK,QAAM,KAAK,YAAU,KAAK,SAAS,OAAO;AAAE,gBAAMH,KAAE,KAAK;AAAO,eAAK,IAAIA,GAAE,KAAGA,GAAE,SAAS,CAAC,IAAE,SAAMG,KAAEH,GAAE,KAAGA,GAAE,SAAS,GAAEA,GAAE,SAAS,KAAG,OAAIG,KAAG,KAAK,IAAIH,GAAE,KAAGA,GAAE,SAAS,CAAC,IAAE,SAAMG,KAAEH,GAAE,KAAGA,GAAE,SAAS,GAAEA,GAAE,SAAS,KAAG,OAAIG,KAAG,KAAK,IAAIH,GAAE,KAAGA,GAAE,SAAS,CAAC,IAAE,SAAMG,KAAEH,GAAE,KAAGA,GAAE,SAAS,GAAEA,GAAE,SAAS,KAAG,OAAIG,KAAGH,GAAE,OAAO,KAAK,YAAY,GAAE,KAAK,KAAG,KAAK,MAAI,CAAC;AAAE,mBAAQG,KAAE,GAAEA,KAAE,KAAK,MAAM,SAAS,WAAW,SAAS,MAAM,QAAOA,MAAG,GAAE;AAAC,kBAAMH,KAAE,EAAC,GAAE,KAAK,MAAM,SAAS,WAAW,SAAS,MAAMG,EAAC,GAAE,GAAE,KAAK,MAAM,SAAS,WAAW,SAAS,MAAMA,KAAE,CAAC,GAAE,GAAE,KAAK,MAAM,SAAS,WAAW,SAAS,MAAMA,KAAE,CAAC,GAAE,IAAG,KAAK,GAAGA,EAAC,EAAC;AAAE,gBAAGH,GAAE,IAAG;AAAC,oBAAMC,KAAE,KAAK,QAAQ,WAAUC,KAAE,KAAK,KAAKD,EAAC,IAAE,KAAK,IAAI,CAACD,GAAE,IAAE,MAAGA,GAAE,CAAC,GAAEK,KAAE,KAAK,IAAIJ,KAAE,KAAK,IAAE,OAAIA,KAAED,GAAE,IAAE,QAAKC,KAAED,GAAE,IAAE,QAAKE,EAAC,GAAEI,KAAE,KAAK,IAAID,KAAE,GAAE,CAAC,IAAE;AAAE,cAAAL,GAAE,IAAEA,GAAE,KAAGM,KAAE,KAAK,QAAQ,YAAW,KAAK,MAAM,SAAS,WAAW,SAAS,MAAMH,KAAE,CAAC,IAAEH,GAAE;AAAA,YAAC,MAAM,MAAK,GAAGG,EAAC,IAAEH,GAAE;AAAA,UAAC;AAAC,eAAK,MAAM,SAAS,WAAW,SAAS,SAAS,EAAE,gBAAgB,GAAE,KAAK,MAAM,SAAS,qBAAqB,GAAE,KAAK,MAAM,SAAS,WAAW,SAAS,cAAY,MAAG,KAAK,cAAY,KAAK,UAAU,SAAS,aAAa,KAAK,MAAM,QAAQ,GAAE,KAAK,UAAU,SAAS,mBAAmB;AAAA,QAAE;AAAA,QAAC,YAAYG,IAAEH,IAAE;AAAC,gBAAMC,KAAE,KAAK;AAAO,iBAAOA,GAAE,OAAKA,GAAE,KAAGA,GAAE,SAAS,GAAEA,GAAE,KAAGA,GAAE,SAAS,GAAEA,GAAE,KAAGA,GAAE,SAAS,IAAGA,GAAE,KAAGA,GAAE,KAAG,OAAKE,KAAE,OAAI,KAAK,QAAQ,MAAKF,GAAE,KAAGA,GAAE,KAAG,QAAMD,KAAE,OAAI,KAAK,QAAQ,MAAKC,GAAE,KAAGA,GAAE,KAAG,OAAKE,KAAE,OAAI,KAAK,QAAQ;AAAA,QAAI;AAAA,MAAC;AAAC,QAAE,UAAU,iBAAe,EAAC,OAAM,OAAM,WAAU,IAAG,YAAW,IAAG,WAAU,GAAE,MAAK,EAAC,GAAE,EAAE,UAAU;AAAE,YAAM,IAAE,EAAE,SAAS,SAAQ,CAAC;AAAE,aAAO;AAAA,IAAC,GAAG,CAAE;AAAA;AAAA;", "names": ["e", "i", "s", "t", "a", "o", "n", "r", "h", "p", "l", "c"]}