/**
 * @license
 * Copyright 2010-2025 Three.js Authors
 * SPDX-License-Identifier: MIT
 */
import{TSL as e}from"three/webgpu";const t=e.BRDF_GGX,r=e.BRDF_Lambert,a=e.BasicPointShadowFilter,o=e.BasicShadowFilter,i=e.Break,n=e.Const,l=e.Continue,s=e.DFGApprox,c=e.D_GGX,m=e.Discard,u=e.EPSILON,p=e.F_<PERSON>hlick,d=e.Fn,g=e.INFINITY,h=e.If,x=e.Loop,b=e.NodeAccess,f=e.NodeShaderStage,w=e.NodeType,v=e.NodeUpdateType,_=e.PCFShadowFilter,S=e.PCFSoftShadowFilter,T=e.PI,y=e.PI2,V=e.PointShadowFilter,M=e.Return,F=e.Schlick_to_F0,D=e.<PERSON>riptableNodeResources,I=e.Shader<PERSON>ode,C=e.<PERSON>,B=e.Switch,P=e.TBNViewMatrix,R=e.VSMShadowFilter,A=e.V_GGX_SmithCorrelated,N=e.Var,O=e.VarIntent,k=e.abs,G=e.acesFilmicToneMapping,L=e.acos,U=e.add,j=e.addMethodChaining,E=e.addNodeElement,W=e.agxToneMapping,q=e.all,z=e.alphaT,X=e.and,Z=e.anisotropy,K=e.anisotropyB,Y=e.anisotropyT,H=e.any,J=e.append,Q=e.array,$=e.arrayBuffer,ee=e.asin,te=e.assign,re=e.atan,ae=e.atan2,oe=e.atomicAdd,ie=e.atomicAnd,ne=e.atomicFunc,le=e.atomicLoad,se=e.atomicMax,ce=e.atomicMin,me=e.atomicOr,ue=e.atomicStore,pe=e.atomicSub,de=e.atomicXor,ge=e.attenuationColor,he=e.attenuationDistance,xe=e.attribute,be=e.attributeArray,fe=e.backgroundBlurriness,we=e.backgroundIntensity,ve=e.backgroundRotation,_e=e.batch,Se=e.bentNormalView,Te=e.billboarding,ye=e.bitAnd,Ve=e.bitNot,Me=e.bitOr,Fe=e.bitXor,De=e.bitangentGeometry,Ie=e.bitangentLocal,Ce=e.bitangentView,Be=e.bitangentWorld,Pe=e.bitcast,Re=e.blendBurn,Ae=e.blendColor,Ne=e.blendDodge,Oe=e.blendOverlay,ke=e.blendScreen,Ge=e.blur,Le=e.bool,Ue=e.buffer,je=e.bufferAttribute,Ee=e.bumpMap,We=e.burn,qe=e.builtin,ze=e.bvec2,Xe=e.bvec3,Ze=e.bvec4,Ke=e.bypass,Ye=e.cache,He=e.call,Je=e.cameraFar,Qe=e.cameraIndex,$e=e.cameraNear,et=e.cameraNormalMatrix,tt=e.cameraPosition,rt=e.cameraProjectionMatrix,at=e.cameraProjectionMatrixInverse,ot=e.cameraViewMatrix,it=e.cameraViewport,nt=e.cameraWorldMatrix,lt=e.cbrt,st=e.cdl,ct=e.ceil,mt=e.checker,ut=e.cineonToneMapping,pt=e.clamp,dt=e.clearcoat,gt=e.clearcoatNormalView,ht=e.clearcoatRoughness,xt=e.code,bt=e.color,ft=e.colorSpaceToWorking,wt=e.colorToDirection,vt=e.compute,_t=e.computeKernel,St=e.computeSkinning,Tt=e.context,yt=e.convert,Vt=e.convertColorSpace,Mt=e.convertToTexture,Ft=e.cos,Dt=e.cross,It=e.cubeTexture,Ct=e.cubeTextureBase,Bt=e.cubeToUV,Pt=e.dFdx,Rt=e.dFdy,At=e.dashSize,Nt=e.debug,Ot=e.decrement,kt=e.decrementBefore,Gt=e.defaultBuildStages,Lt=e.defaultShaderStages,Ut=e.defined,jt=e.degrees,Et=e.deltaTime,Wt=e.densityFog,qt=e.densityFogFactor,zt=e.depth,Xt=e.depthPass,Zt=e.determinant,Kt=e.difference,Yt=e.diffuseColor,Ht=e.directPointLight,Jt=e.directionToColor,Qt=e.directionToFaceDirection,$t=e.dispersion,er=e.distance,tr=e.div,rr=e.dodge,ar=e.dot,or=e.drawIndex,ir=e.dynamicBufferAttribute,nr=e.element,lr=e.emissive,sr=e.equal,cr=e.equals,mr=e.equirectUV,ur=e.exp,pr=e.exp2,dr=e.expression,gr=e.faceDirection,hr=e.faceForward,xr=e.faceforward,br=e.float,fr=e.floatBitsToInt,wr=e.floatBitsToUint,vr=e.floor,_r=e.fog,Sr=e.fract,Tr=e.frameGroup,yr=e.frameId,Vr=e.frontFacing,Mr=e.fwidth,Fr=e.gain,Dr=e.gapSize,Ir=e.getConstNodeType,Cr=e.getCurrentStack,Br=e.getDirection,Pr=e.getDistanceAttenuation,Rr=e.getGeometryRoughness,Ar=e.getNormalFromDepth,Nr=e.getParallaxCorrectNormal,Or=e.getRoughness,kr=e.getScreenPosition,Gr=e.getShIrradianceAt,Lr=e.getShadowMaterial,Ur=e.getShadowRenderObjectFunction,jr=e.getTextureIndex,Er=e.getViewPosition,Wr=e.globalId,qr=e.glsl,zr=e.glslFn,Xr=e.grayscale,Zr=e.greaterThan,Kr=e.greaterThanEqual,Yr=e.hash,Hr=e.highpModelNormalViewMatrix,Jr=e.highpModelViewMatrix,Qr=e.hue,$r=e.increment,ea=e.incrementBefore,ta=e.instance,ra=e.instanceIndex,aa=e.instancedArray,oa=e.instancedBufferAttribute,ia=e.instancedDynamicBufferAttribute,na=e.instancedMesh,la=e.int,sa=e.intBitsToFloat,ca=e.inverse,ma=e.inverseSqrt,ua=e.inversesqrt,pa=e.invocationLocalIndex,da=e.invocationSubgroupIndex,ga=e.ior,ha=e.iridescence,xa=e.iridescenceIOR,ba=e.iridescenceThickness,fa=e.ivec2,wa=e.ivec3,va=e.ivec4,_a=e.js,Sa=e.label,Ta=e.length,ya=e.lengthSq,Va=e.lessThan,Ma=e.lessThanEqual,Fa=e.lightPosition,Da=e.lightProjectionUV,Ia=e.lightShadowMatrix,Ca=e.lightTargetDirection,Ba=e.lightTargetPosition,Pa=e.lightViewPosition,Ra=e.lightingContext,Aa=e.lights,Na=e.linearDepth,Oa=e.linearToneMapping,ka=e.localId,Ga=e.log,La=e.log2,Ua=e.logarithmicDepthToViewZ,ja=e.luminance,Ea=e.mat2,Wa=e.mat3,qa=e.mat4,za=e.matcapUV,Xa=e.materialAO,Za=e.materialAlphaTest,Ka=e.materialAnisotropy,Ya=e.materialAnisotropyVector,Ha=e.materialAttenuationColor,Ja=e.materialAttenuationDistance,Qa=e.materialClearcoat,$a=e.materialClearcoatNormal,eo=e.materialClearcoatRoughness,to=e.materialColor,ro=e.materialDispersion,ao=e.materialEmissive,oo=e.materialEnvIntensity,io=e.materialEnvRotation,no=e.materialIOR,lo=e.materialIridescence,so=e.materialIridescenceIOR,co=e.materialIridescenceThickness,mo=e.materialLightMap,uo=e.materialLineDashOffset,po=e.materialLineDashSize,go=e.materialLineGapSize,ho=e.materialLineScale,xo=e.materialLineWidth,bo=e.materialMetalness,fo=e.materialNormal,wo=e.materialOpacity,vo=e.materialPointSize,_o=e.materialReference,So=e.materialReflectivity,To=e.materialRefractionRatio,yo=e.materialRotation,Vo=e.materialRoughness,Mo=e.materialSheen,Fo=e.materialSheenRoughness,Do=e.materialShininess,Io=e.materialSpecular,Co=e.materialSpecularColor,Bo=e.materialSpecularIntensity,Po=e.materialSpecularStrength,Ro=e.materialThickness,Ao=e.materialTransmission,No=e.max,Oo=e.maxMipLevel,ko=e.mediumpModelViewMatrix,Go=e.metalness,Lo=e.min,Uo=e.mix,jo=e.mixElement,Eo=e.mod,Wo=e.modInt,qo=e.modelDirection,zo=e.modelNormalMatrix,Xo=e.modelPosition,Zo=e.modelRadius,Ko=e.modelScale,Yo=e.modelViewMatrix,Ho=e.modelViewPosition,Jo=e.modelViewProjection,Qo=e.modelWorldMatrix,$o=e.modelWorldMatrixInverse,ei=e.morphReference,ti=e.mrt,ri=e.mul,ai=e.mx_aastep,oi=e.mx_add,ii=e.mx_atan2,ni=e.mx_cell_noise_float,li=e.mx_contrast,si=e.mx_divide,ci=e.mx_fractal_noise_float,mi=e.mx_fractal_noise_vec2,ui=e.mx_fractal_noise_vec3,pi=e.mx_fractal_noise_vec4,di=e.mx_frame,gi=e.mx_heighttonormal,hi=e.mx_hsvtorgb,xi=e.mx_ifequal,bi=e.mx_ifgreater,fi=e.mx_ifgreatereq,wi=e.mx_invert,vi=e.mx_modulo,_i=e.mx_multiply,Si=e.mx_noise_float,Ti=e.mx_noise_vec3,yi=e.mx_noise_vec4,Vi=e.mx_place2d,Mi=e.mx_power,Fi=e.mx_ramp4,Di=e.mx_ramplr,Ii=e.mx_ramptb,Ci=e.mx_rgbtohsv,Bi=e.mx_rotate2d,Pi=e.mx_rotate3d,Ri=e.mx_safepower,Ai=e.mx_separate,Ni=e.mx_splitlr,Oi=e.mx_splittb,ki=e.mx_srgb_texture_to_lin_rec709,Gi=e.mx_subtract,Li=e.mx_timer,Ui=e.mx_transform_uv,ji=e.mx_unifiednoise2d,Ei=e.mx_unifiednoise3d,Wi=e.mx_worley_noise_float,qi=e.mx_worley_noise_vec2,zi=e.mx_worley_noise_vec3,Xi=e.negate,Zi=e.neutralToneMapping,Ki=e.nodeArray,Yi=e.nodeImmutable,Hi=e.nodeObject,Ji=e.nodeObjectIntent,Qi=e.nodeObjects,$i=e.nodeProxy,en=e.nodeProxyIntent,tn=e.normalFlat,rn=e.normalGeometry,an=e.normalLocal,on=e.normalMap,nn=e.normalView,ln=e.normalViewGeometry,sn=e.normalWorld,cn=e.normalWorldGeometry,mn=e.normalize,un=e.not,pn=e.notEqual,dn=e.numWorkgroups,gn=e.objectDirection,hn=e.objectGroup,xn=e.objectPosition,bn=e.objectRadius,fn=e.objectScale,wn=e.objectViewPosition,vn=e.objectWorldMatrix,_n=e.OnObjectUpdate,Sn=e.OnMaterialUpdate,Tn=e.oneMinus,yn=e.or,Vn=e.orthographicDepthToViewZ,Mn=e.oscSawtooth,Fn=e.oscSine,Dn=e.oscSquare,In=e.oscTriangle,Cn=e.output,Bn=e.outputStruct,Pn=e.overlay,Rn=e.overloadingFn,An=e.parabola,Nn=e.parallaxDirection,On=e.parallaxUV,kn=e.parameter,Gn=e.pass,Ln=e.passTexture,Un=e.pcurve,jn=e.perspectiveDepthToViewZ,En=e.pmremTexture,Wn=e.pointShadow,qn=e.pointUV,zn=e.pointWidth,Xn=e.positionGeometry,Zn=e.positionLocal,Kn=e.positionPrevious,Yn=e.positionView,Hn=e.positionViewDirection,Jn=e.positionWorld,Qn=e.positionWorldDirection,$n=e.posterize,el=e.pow,tl=e.pow2,rl=e.pow3,al=e.pow4,ol=e.premultiplyAlpha,il=e.property,nl=e.radians,ll=e.rand,sl=e.range,cl=e.rangeFog,ml=e.rangeFogFactor,ul=e.reciprocal,pl=e.reference,dl=e.referenceBuffer,gl=e.reflect,hl=e.reflectVector,xl=e.reflectView,bl=e.reflector,fl=e.refract,wl=e.refractVector,vl=e.refractView,_l=e.reinhardToneMapping,Sl=e.remap,Tl=e.remapClamp,yl=e.renderGroup,Vl=e.renderOutput,Ml=e.rendererReference,Fl=e.rotate,Dl=e.rotateUV,Il=e.roughness,Cl=e.round,Bl=e.rtt,Pl=e.sRGBTransferEOTF,Rl=e.sRGBTransferOETF,Al=e.sample,Nl=e.sampler,Ol=e.samplerComparison,kl=e.saturate,Gl=e.saturation,Ll=e.screen,Ul=e.screenCoordinate,jl=e.screenDPR,El=e.screenSize,Wl=e.screenUV,ql=e.scriptable,zl=e.scriptableValue,Xl=e.select,Zl=e.setCurrentStack,Kl=e.setName,Yl=e.shaderStages,Hl=e.shadow,Jl=e.shadowPositionWorld,Ql=e.shapeCircle,$l=e.sharedUniformGroup,es=e.sheen,ts=e.sheenRoughness,rs=e.shiftLeft,as=e.shiftRight,os=e.shininess,is=e.sign,ns=e.sin,ls=e.sinc,ss=e.skinning,cs=e.smoothstep,ms=e.smoothstepElement,us=e.specularColor,ps=e.specularF90,ds=e.spherizeUV,gs=e.split,hs=e.spritesheetUV,xs=e.sqrt,bs=e.stack,fs=e.step,ws=e.stepElement,vs=e.storage,_s=e.storageBarrier,Ss=e.storageObject,Ts=e.storageTexture,ys=e.string,Vs=e.struct,Ms=e.sub,Fs=e.subgroupAdd,Ds=e.subgroupAll,Is=e.subgroupAnd,Cs=e.subgroupAny,Bs=e.subgroupBallot,Ps=e.subgroupBroadcast,Rs=e.subgroupBroadcastFirst,As=e.subBuild,Ns=e.subgroupElect,Os=e.subgroupExclusiveAdd,ks=e.subgroupExclusiveMul,Gs=e.subgroupInclusiveAdd,Ls=e.subgroupInclusiveMul,Us=e.subgroupIndex,js=e.subgroupMax,Es=e.subgroupMin,Ws=e.subgroupMul,qs=e.subgroupOr,zs=e.subgroupShuffle,Xs=e.subgroupShuffleDown,Zs=e.subgroupShuffleUp,Ks=e.subgroupShuffleXor,Ys=e.subgroupSize,Hs=e.subgroupXor,Js=e.tan,Qs=e.tangentGeometry,$s=e.tangentLocal,ec=e.tangentView,tc=e.tangentWorld,rc=e.texture,ac=e.texture3D,oc=e.textureBarrier,ic=e.textureBicubic,nc=e.textureBicubicLevel,lc=e.textureCubeUV,sc=e.textureLoad,cc=e.textureSize,mc=e.textureStore,uc=e.thickness,pc=e.time,dc=e.toneMapping,gc=e.toneMappingExposure,hc=e.toonOutlinePass,xc=e.transformDirection,bc=e.transformNormal,fc=e.transformNormalToView,wc=e.transformedClearcoatNormalView,vc=e.transformedNormalView,_c=e.transformedNormalWorld,Sc=e.transmission,Tc=e.transpose,yc=e.triNoise3D,Vc=e.triplanarTexture,Mc=e.triplanarTextures,Fc=e.trunc,Dc=e.uint,Ic=e.uintBitsToFloat,Cc=e.uniform,Bc=e.uniformArray,Pc=e.uniformCubeTexture,Rc=e.uniformGroup,Ac=e.uniformFlow,Nc=e.uniformTexture,Oc=e.unpremultiplyAlpha,kc=e.userData,Gc=e.uv,Lc=e.uvec2,Uc=e.uvec3,jc=e.uvec4,Ec=e.varying,Wc=e.varyingProperty,qc=e.vec2,zc=e.vec3,Xc=e.vec4,Zc=e.vectorComponents,Kc=e.velocity,Yc=e.vertexColor,Hc=e.vertexIndex,Jc=e.vertexStage,Qc=e.vibrance,$c=e.viewZToLogarithmicDepth,em=e.viewZToOrthographicDepth,tm=e.viewZToPerspectiveDepth,rm=e.viewport,am=e.viewportCoordinate,om=e.viewportDepthTexture,im=e.viewportLinearDepth,nm=e.viewportMipTexture,lm=e.viewportResolution,sm=e.viewportSafeUV,cm=e.viewportSharedTexture,mm=e.viewportSize,um=e.viewportTexture,pm=e.viewportUV,dm=e.wgsl,gm=e.wgslFn,hm=e.workgroupArray,xm=e.workgroupBarrier,bm=e.workgroupId,fm=e.workingToColorSpace,wm=e.xor;export{t as BRDF_GGX,r as BRDF_Lambert,a as BasicPointShadowFilter,o as BasicShadowFilter,i as Break,n as Const,l as Continue,s as DFGApprox,c as D_GGX,m as Discard,u as EPSILON,p as F_Schlick,d as Fn,g as INFINITY,h as If,x as Loop,b as NodeAccess,f as NodeShaderStage,w as NodeType,v as NodeUpdateType,Sn as OnMaterialUpdate,_n as OnObjectUpdate,_ as PCFShadowFilter,S as PCFSoftShadowFilter,T as PI,y as PI2,V as PointShadowFilter,M as Return,F as Schlick_to_F0,D as ScriptableNodeResources,I as ShaderNode,C as Stack,B as Switch,P as TBNViewMatrix,R as VSMShadowFilter,A as V_GGX_SmithCorrelated,N as Var,O as VarIntent,k as abs,G as acesFilmicToneMapping,L as acos,U as add,j as addMethodChaining,E as addNodeElement,W as agxToneMapping,q as all,z as alphaT,X as and,Z as anisotropy,K as anisotropyB,Y as anisotropyT,H as any,J as append,Q as array,$ as arrayBuffer,ee as asin,te as assign,re as atan,ae as atan2,oe as atomicAdd,ie as atomicAnd,ne as atomicFunc,le as atomicLoad,se as atomicMax,ce as atomicMin,me as atomicOr,ue as atomicStore,pe as atomicSub,de as atomicXor,ge as attenuationColor,he as attenuationDistance,xe as attribute,be as attributeArray,fe as backgroundBlurriness,we as backgroundIntensity,ve as backgroundRotation,_e as batch,Se as bentNormalView,Te as billboarding,ye as bitAnd,Ve as bitNot,Me as bitOr,Fe as bitXor,De as bitangentGeometry,Ie as bitangentLocal,Ce as bitangentView,Be as bitangentWorld,Pe as bitcast,Re as blendBurn,Ae as blendColor,Ne as blendDodge,Oe as blendOverlay,ke as blendScreen,Ge as blur,Le as bool,Ue as buffer,je as bufferAttribute,qe as builtin,Ee as bumpMap,We as burn,ze as bvec2,Xe as bvec3,Ze as bvec4,Ke as bypass,Ye as cache,He as call,Je as cameraFar,Qe as cameraIndex,$e as cameraNear,et as cameraNormalMatrix,tt as cameraPosition,rt as cameraProjectionMatrix,at as cameraProjectionMatrixInverse,ot as cameraViewMatrix,it as cameraViewport,nt as cameraWorldMatrix,lt as cbrt,st as cdl,ct as ceil,mt as checker,ut as cineonToneMapping,pt as clamp,dt as clearcoat,gt as clearcoatNormalView,ht as clearcoatRoughness,xt as code,bt as color,ft as colorSpaceToWorking,wt as colorToDirection,vt as compute,_t as computeKernel,St as computeSkinning,Tt as context,yt as convert,Vt as convertColorSpace,Mt as convertToTexture,Ft as cos,Dt as cross,It as cubeTexture,Ct as cubeTextureBase,Bt as cubeToUV,Pt as dFdx,Rt as dFdy,At as dashSize,Nt as debug,Ot as decrement,kt as decrementBefore,Gt as defaultBuildStages,Lt as defaultShaderStages,Ut as defined,jt as degrees,Et as deltaTime,Wt as densityFog,qt as densityFogFactor,zt as depth,Xt as depthPass,Zt as determinant,Kt as difference,Yt as diffuseColor,Ht as directPointLight,Jt as directionToColor,Qt as directionToFaceDirection,$t as dispersion,er as distance,tr as div,rr as dodge,ar as dot,or as drawIndex,ir as dynamicBufferAttribute,nr as element,lr as emissive,sr as equal,cr as equals,mr as equirectUV,ur as exp,pr as exp2,dr as expression,gr as faceDirection,hr as faceForward,xr as faceforward,br as float,fr as floatBitsToInt,wr as floatBitsToUint,vr as floor,_r as fog,Sr as fract,Tr as frameGroup,yr as frameId,Vr as frontFacing,Mr as fwidth,Fr as gain,Dr as gapSize,Ir as getConstNodeType,Cr as getCurrentStack,Br as getDirection,Pr as getDistanceAttenuation,Rr as getGeometryRoughness,Ar as getNormalFromDepth,Nr as getParallaxCorrectNormal,Or as getRoughness,kr as getScreenPosition,Gr as getShIrradianceAt,Lr as getShadowMaterial,Ur as getShadowRenderObjectFunction,jr as getTextureIndex,Er as getViewPosition,Wr as globalId,qr as glsl,zr as glslFn,Xr as grayscale,Zr as greaterThan,Kr as greaterThanEqual,Yr as hash,Hr as highpModelNormalViewMatrix,Jr as highpModelViewMatrix,Qr as hue,$r as increment,ea as incrementBefore,ta as instance,ra as instanceIndex,aa as instancedArray,oa as instancedBufferAttribute,ia as instancedDynamicBufferAttribute,na as instancedMesh,la as int,sa as intBitsToFloat,ca as inverse,ma as inverseSqrt,ua as inversesqrt,pa as invocationLocalIndex,da as invocationSubgroupIndex,ga as ior,ha as iridescence,xa as iridescenceIOR,ba as iridescenceThickness,fa as ivec2,wa as ivec3,va as ivec4,_a as js,Sa as label,Ta as length,ya as lengthSq,Va as lessThan,Ma as lessThanEqual,Fa as lightPosition,Da as lightProjectionUV,Ia as lightShadowMatrix,Ca as lightTargetDirection,Ba as lightTargetPosition,Pa as lightViewPosition,Ra as lightingContext,Aa as lights,Na as linearDepth,Oa as linearToneMapping,ka as localId,Ga as log,La as log2,Ua as logarithmicDepthToViewZ,ja as luminance,Ea as mat2,Wa as mat3,qa as mat4,za as matcapUV,Xa as materialAO,Za as materialAlphaTest,Ka as materialAnisotropy,Ya as materialAnisotropyVector,Ha as materialAttenuationColor,Ja as materialAttenuationDistance,Qa as materialClearcoat,$a as materialClearcoatNormal,eo as materialClearcoatRoughness,to as materialColor,ro as materialDispersion,ao as materialEmissive,oo as materialEnvIntensity,io as materialEnvRotation,no as materialIOR,lo as materialIridescence,so as materialIridescenceIOR,co as materialIridescenceThickness,mo as materialLightMap,uo as materialLineDashOffset,po as materialLineDashSize,go as materialLineGapSize,ho as materialLineScale,xo as materialLineWidth,bo as materialMetalness,fo as materialNormal,wo as materialOpacity,vo as materialPointSize,_o as materialReference,So as materialReflectivity,To as materialRefractionRatio,yo as materialRotation,Vo as materialRoughness,Mo as materialSheen,Fo as materialSheenRoughness,Do as materialShininess,Io as materialSpecular,Co as materialSpecularColor,Bo as materialSpecularIntensity,Po as materialSpecularStrength,Ro as materialThickness,Ao as materialTransmission,No as max,Oo as maxMipLevel,ko as mediumpModelViewMatrix,Go as metalness,Lo as min,Uo as mix,jo as mixElement,Eo as mod,Wo as modInt,qo as modelDirection,zo as modelNormalMatrix,Xo as modelPosition,Zo as modelRadius,Ko as modelScale,Yo as modelViewMatrix,Ho as modelViewPosition,Jo as modelViewProjection,Qo as modelWorldMatrix,$o as modelWorldMatrixInverse,ei as morphReference,ti as mrt,ri as mul,ai as mx_aastep,oi as mx_add,ii as mx_atan2,ni as mx_cell_noise_float,li as mx_contrast,si as mx_divide,ci as mx_fractal_noise_float,mi as mx_fractal_noise_vec2,ui as mx_fractal_noise_vec3,pi as mx_fractal_noise_vec4,di as mx_frame,gi as mx_heighttonormal,hi as mx_hsvtorgb,xi as mx_ifequal,bi as mx_ifgreater,fi as mx_ifgreatereq,wi as mx_invert,vi as mx_modulo,_i as mx_multiply,Si as mx_noise_float,Ti as mx_noise_vec3,yi as mx_noise_vec4,Vi as mx_place2d,Mi as mx_power,Fi as mx_ramp4,Di as mx_ramplr,Ii as mx_ramptb,Ci as mx_rgbtohsv,Bi as mx_rotate2d,Pi as mx_rotate3d,Ri as mx_safepower,Ai as mx_separate,Ni as mx_splitlr,Oi as mx_splittb,ki as mx_srgb_texture_to_lin_rec709,Gi as mx_subtract,Li as mx_timer,Ui as mx_transform_uv,ji as mx_unifiednoise2d,Ei as mx_unifiednoise3d,Wi as mx_worley_noise_float,qi as mx_worley_noise_vec2,zi as mx_worley_noise_vec3,Xi as negate,Zi as neutralToneMapping,Ki as nodeArray,Yi as nodeImmutable,Hi as nodeObject,Ji as nodeObjectIntent,Qi as nodeObjects,$i as nodeProxy,en as nodeProxyIntent,tn as normalFlat,rn as normalGeometry,an as normalLocal,on as normalMap,nn as normalView,ln as normalViewGeometry,sn as normalWorld,cn as normalWorldGeometry,mn as normalize,un as not,pn as notEqual,dn as numWorkgroups,gn as objectDirection,hn as objectGroup,xn as objectPosition,bn as objectRadius,fn as objectScale,wn as objectViewPosition,vn as objectWorldMatrix,Tn as oneMinus,yn as or,Vn as orthographicDepthToViewZ,Mn as oscSawtooth,Fn as oscSine,Dn as oscSquare,In as oscTriangle,Cn as output,Bn as outputStruct,Pn as overlay,Rn as overloadingFn,An as parabola,Nn as parallaxDirection,On as parallaxUV,kn as parameter,Gn as pass,Ln as passTexture,Un as pcurve,jn as perspectiveDepthToViewZ,En as pmremTexture,Wn as pointShadow,qn as pointUV,zn as pointWidth,Xn as positionGeometry,Zn as positionLocal,Kn as positionPrevious,Yn as positionView,Hn as positionViewDirection,Jn as positionWorld,Qn as positionWorldDirection,$n as posterize,el as pow,tl as pow2,rl as pow3,al as pow4,ol as premultiplyAlpha,il as property,nl as radians,ll as rand,sl as range,cl as rangeFog,ml as rangeFogFactor,ul as reciprocal,pl as reference,dl as referenceBuffer,gl as reflect,hl as reflectVector,xl as reflectView,bl as reflector,fl as refract,wl as refractVector,vl as refractView,_l as reinhardToneMapping,Sl as remap,Tl as remapClamp,yl as renderGroup,Vl as renderOutput,Ml as rendererReference,Fl as rotate,Dl as rotateUV,Il as roughness,Cl as round,Bl as rtt,Pl as sRGBTransferEOTF,Rl as sRGBTransferOETF,Al as sample,Nl as sampler,Ol as samplerComparison,kl as saturate,Gl as saturation,Ll as screen,Ul as screenCoordinate,jl as screenDPR,El as screenSize,Wl as screenUV,ql as scriptable,zl as scriptableValue,Xl as select,Zl as setCurrentStack,Kl as setName,Yl as shaderStages,Hl as shadow,Jl as shadowPositionWorld,Ql as shapeCircle,$l as sharedUniformGroup,es as sheen,ts as sheenRoughness,rs as shiftLeft,as as shiftRight,os as shininess,is as sign,ns as sin,ls as sinc,ss as skinning,cs as smoothstep,ms as smoothstepElement,us as specularColor,ps as specularF90,ds as spherizeUV,gs as split,hs as spritesheetUV,xs as sqrt,bs as stack,fs as step,ws as stepElement,vs as storage,_s as storageBarrier,Ss as storageObject,Ts as storageTexture,ys as string,Vs as struct,Ms as sub,As as subBuild,Fs as subgroupAdd,Ds as subgroupAll,Is as subgroupAnd,Cs as subgroupAny,Bs as subgroupBallot,Ps as subgroupBroadcast,Rs as subgroupBroadcastFirst,Ns as subgroupElect,Os as subgroupExclusiveAdd,ks as subgroupExclusiveMul,Gs as subgroupInclusiveAdd,Ls as subgroupInclusiveMul,Us as subgroupIndex,js as subgroupMax,Es as subgroupMin,Ws as subgroupMul,qs as subgroupOr,zs as subgroupShuffle,Xs as subgroupShuffleDown,Zs as subgroupShuffleUp,Ks as subgroupShuffleXor,Ys as subgroupSize,Hs as subgroupXor,Js as tan,Qs as tangentGeometry,$s as tangentLocal,ec as tangentView,tc as tangentWorld,rc as texture,ac as texture3D,oc as textureBarrier,ic as textureBicubic,nc as textureBicubicLevel,lc as textureCubeUV,sc as textureLoad,cc as textureSize,mc as textureStore,uc as thickness,pc as time,dc as toneMapping,gc as toneMappingExposure,hc as toonOutlinePass,xc as transformDirection,bc as transformNormal,fc as transformNormalToView,wc as transformedClearcoatNormalView,vc as transformedNormalView,_c as transformedNormalWorld,Sc as transmission,Tc as transpose,yc as triNoise3D,Vc as triplanarTexture,Mc as triplanarTextures,Fc as trunc,Dc as uint,Ic as uintBitsToFloat,Cc as uniform,Bc as uniformArray,Pc as uniformCubeTexture,Ac as uniformFlow,Rc as uniformGroup,Nc as uniformTexture,Oc as unpremultiplyAlpha,kc as userData,Gc as uv,Lc as uvec2,Uc as uvec3,jc as uvec4,Ec as varying,Wc as varyingProperty,qc as vec2,zc as vec3,Xc as vec4,Zc as vectorComponents,Kc as velocity,Yc as vertexColor,Hc as vertexIndex,Jc as vertexStage,Qc as vibrance,$c as viewZToLogarithmicDepth,em as viewZToOrthographicDepth,tm as viewZToPerspectiveDepth,rm as viewport,am as viewportCoordinate,om as viewportDepthTexture,im as viewportLinearDepth,nm as viewportMipTexture,lm as viewportResolution,sm as viewportSafeUV,cm as viewportSharedTexture,mm as viewportSize,um as viewportTexture,pm as viewportUV,dm as wgsl,gm as wgslFn,hm as workgroupArray,xm as workgroupBarrier,bm as workgroupId,fm as workingToColorSpace,wm as xor};
