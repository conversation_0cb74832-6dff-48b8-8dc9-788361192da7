// component/VantaBackground.jsx
import React, { useEffect, useRef } from "react";
import WAVES from "vanta/dist/vanta.waves.min";
import * as THREE from "three";

export default function VantaBackground({ children }) {
  const vantaRef = useRef(null);
  const effect = useRef(null);

  useEffect(() => {
    if (!effect.current) {
      effect.current = WAVES({
        el: vantaRef.current,
        THREE: THREE,
        mouseControls: true,
        touchControls: true,
        minHeight: 200.0,
        minWidth: 200.0,
        scale: 1.0,
        scaleMobile: 1.0,
        color: 0x1e40af,
        shininess: 50,
        waveHeight: 20,
        waveSpeed: 1.0,
      });
    }
    return () => {
      if (effect.current) effect.current.destroy();
    };
  }, []);

  return (
    <div
      ref={vantaRef}
      className="w-full h-screen flex items-center justify-center relative"
    >
      <div className="z-10">{children}</div>
    </div>
  );
}
